"""
Knowledge Graph construction script for the complete MIR-Flickr-25K dataset.
This script builds a comprehensive knowledge graph from 25,000 images and their annotations.
"""

import os
import json
import numpy as np
import pickle
from collections import defaultdict, Counter
import logging
from pathlib import Path
from configs.mirflickr_config import MIRFLICKR_DATASET_CONFIG

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MIRFlickr25KKnowledgeGraphBuilder:
    def __init__(self, data_path, output_path):
        self.data_path = data_path
        self.output_path = output_path
        self.processed_path = os.path.join(data_path, 'processed')
        
        # Initialize data structures
        self.entities = {}  # entity_name -> entity_id
        self.relations = {
            'image_concept': 0,
            'image_tag': 1,
            'tag_concept': 2,
            'concept_cooccur': 3,
            'tag_cooccur': 4
        }
        self.triples = []
        self.entity_counter = 0
        
        # Load concepts
        self.concepts = [
            'animals', 'baby', 'bird', 'car', 'clouds', 'dog', 'female', 'flower', 
            'food', 'indoor', 'lake', 'male', 'night', 'people', 'plant_life', 
            'portrait', 'river', 'sea', 'sky', 'structures', 'sunset', 'transport', 
            'tree', 'water'
        ]
        
        os.makedirs(output_path, exist_ok=True)
    
    def add_entity(self, entity_name):
        """Add an entity to the knowledge graph."""
        if entity_name not in self.entities:
            self.entities[entity_name] = self.entity_counter
            self.entity_counter += 1
        return self.entities[entity_name]
    
    def add_triple(self, head, relation, tail):
        """Add a triple to the knowledge graph."""
        head_id = self.add_entity(head)
        tail_id = self.add_entity(tail)
        relation_id = self.relations[relation]
        self.triples.append((head_id, relation_id, tail_id))
    
    def load_annotations(self):
        """Load processed annotations."""
        logger.info("Loading processed annotations...")
        
        annotations = {}
        for split in ['train', 'val', 'test']:
            ann_file = os.path.join(self.processed_path, 'annotations', f'{split}_annotations.npy')
            annotations[split] = np.load(ann_file)
        
        return annotations
    
    def load_tags(self):
        """Load processed tags."""
        logger.info("Loading processed tags...")
        
        all_tags = {}
        for split in ['train', 'val', 'test']:
            tags_file = os.path.join(self.processed_path, 'tags', f'{split}_tags.json')
            with open(tags_file, 'r') as f:
                split_tags = json.load(f)
                all_tags.update(split_tags)
        
        return all_tags
    
    def load_split_indices(self):
        """Load train/val/test split indices."""
        logger.info("Loading split indices...")
        
        splits = {}
        for split in ['train', 'val', 'test']:
            split_file = os.path.join(self.processed_path, f'{split}_ids_mirflickr_25k.txt')
            with open(split_file, 'r') as f:
                indices = [int(line.strip()) for line in f]
                splits[split] = indices
        
        return splits
    
    def extract_frequent_tags(self, all_tags, min_frequency=10):
        """Extract frequently occurring tags."""
        logger.info("Extracting frequent tags...")
        
        # Count tag frequencies
        tag_counter = Counter()
        for img_id, tags_str in all_tags.items():
            if tags_str:
                tags = tags_str.split()
                tag_counter.update(tags)
        
        # Filter frequent tags
        frequent_tags = [tag for tag, count in tag_counter.items() if count >= min_frequency]
        logger.info(f"Found {len(frequent_tags)} frequent tags (min_frequency={min_frequency})")
        
        return frequent_tags
    
    def build_image_concept_relations(self, annotations, splits):
        """Build image-concept relations."""
        logger.info("Building image-concept relations...")
        
        relation_count = 0
        for split, split_annotations in annotations.items():
            split_indices = splits[split]
            
            for i, img_idx in enumerate(split_indices):
                img_entity = f"image_{img_idx}"
                
                # Add relations for each concept
                for j, concept in enumerate(self.concepts):
                    if split_annotations[i, j] == 1:
                        concept_entity = f"concept_{concept}"
                        self.add_triple(img_entity, 'image_concept', concept_entity)
                        relation_count += 1
        
        logger.info(f"Added {relation_count} image-concept relations")
    
    def build_image_tag_relations(self, all_tags, frequent_tags):
        """Build image-tag relations."""
        logger.info("Building image-tag relations...")
        
        relation_count = 0
        for img_id, tags_str in all_tags.items():
            if tags_str:
                img_entity = f"image_{img_id}"
                tags = tags_str.split()
                
                for tag in tags:
                    if tag in frequent_tags:
                        tag_entity = f"tag_{tag}"
                        self.add_triple(img_entity, 'image_tag', tag_entity)
                        relation_count += 1
        
        logger.info(f"Added {relation_count} image-tag relations")
    
    def build_tag_concept_relations(self, all_tags, annotations, splits, frequent_tags):
        """Build tag-concept relations based on co-occurrence."""
        logger.info("Building tag-concept relations...")
        
        # Count tag-concept co-occurrences
        tag_concept_cooccur = defaultdict(lambda: defaultdict(int))
        
        for split, split_annotations in annotations.items():
            split_indices = splits[split]
            
            for i, img_idx in enumerate(split_indices):
                img_tags_str = all_tags.get(str(img_idx), "")
                if img_tags_str:
                    img_tags = img_tags_str.split()
                    
                    # For each concept present in this image
                    for j, concept in enumerate(self.concepts):
                        if split_annotations[i, j] == 1:
                            # Count co-occurrence with tags
                            for tag in img_tags:
                                if tag in frequent_tags:
                                    tag_concept_cooccur[tag][concept] += 1
        
        # Add relations for strong co-occurrences
        relation_count = 0
        min_cooccur = 5  # Minimum co-occurrence threshold
        
        for tag, concept_counts in tag_concept_cooccur.items():
            for concept, count in concept_counts.items():
                if count >= min_cooccur:
                    tag_entity = f"tag_{tag}"
                    concept_entity = f"concept_{concept}"
                    self.add_triple(tag_entity, 'tag_concept', concept_entity)
                    relation_count += 1
        
        logger.info(f"Added {relation_count} tag-concept relations")
    
    def build_concept_cooccurrence_relations(self, annotations, splits):
        """Build concept co-occurrence relations."""
        logger.info("Building concept co-occurrence relations...")
        
        # Count concept co-occurrences
        concept_cooccur = defaultdict(lambda: defaultdict(int))
        
        for split, split_annotations in annotations.items():
            for i in range(split_annotations.shape[0]):
                present_concepts = [j for j in range(len(self.concepts)) if split_annotations[i, j] == 1]
                
                # Add co-occurrence for each pair
                for c1 in present_concepts:
                    for c2 in present_concepts:
                        if c1 != c2:
                            concept_cooccur[c1][c2] += 1
        
        # Add relations for strong co-occurrences
        relation_count = 0
        min_cooccur = 50  # Minimum co-occurrence threshold
        
        for c1, c2_counts in concept_cooccur.items():
            for c2, count in c2_counts.items():
                if count >= min_cooccur:
                    concept1_entity = f"concept_{self.concepts[c1]}"
                    concept2_entity = f"concept_{self.concepts[c2]}"
                    self.add_triple(concept1_entity, 'concept_cooccur', concept2_entity)
                    relation_count += 1
        
        logger.info(f"Added {relation_count} concept co-occurrence relations")
    
    def build_tag_cooccurrence_relations(self, all_tags, frequent_tags):
        """Build tag co-occurrence relations."""
        logger.info("Building tag co-occurrence relations...")
        
        # Count tag co-occurrences
        tag_cooccur = defaultdict(lambda: defaultdict(int))
        
        for img_id, tags_str in all_tags.items():
            if tags_str:
                tags = [tag for tag in tags_str.split() if tag in frequent_tags]
                
                # Add co-occurrence for each pair
                for t1 in tags:
                    for t2 in tags:
                        if t1 != t2:
                            tag_cooccur[t1][t2] += 1
        
        # Add relations for strong co-occurrences
        relation_count = 0
        min_cooccur = 20  # Minimum co-occurrence threshold
        
        for t1, t2_counts in tag_cooccur.items():
            for t2, count in t2_counts.items():
                if count >= min_cooccur:
                    tag1_entity = f"tag_{t1}"
                    tag2_entity = f"tag_{t2}"
                    self.add_triple(tag1_entity, 'tag_cooccur', tag2_entity)
                    relation_count += 1
        
        logger.info(f"Added {relation_count} tag co-occurrence relations")
    
    def save_knowledge_graph(self):
        """Save the constructed knowledge graph."""
        logger.info("Saving knowledge graph...")
        
        # Save entities
        entity_file = os.path.join(self.output_path, 'mirflickr_25k_entity2id.txt')
        with open(entity_file, 'w') as f:
            f.write("entity\tid\n")
            for entity, entity_id in sorted(self.entities.items(), key=lambda x: x[1]):
                f.write(f"{entity}\t{entity_id}\n")
        
        # Save relations
        relation_file = os.path.join(self.output_path, 'mirflickr_25k_relation2id.txt')
        with open(relation_file, 'w') as f:
            f.write("relation\tid\n")
            for relation, relation_id in self.relations.items():
                f.write(f"{relation}\t{relation_id}\n")
        
        # Save triples
        triple_file = os.path.join(self.output_path, 'mirflickr_25k_triple.txt')
        with open(triple_file, 'w') as f:
            for head, relation, tail in self.triples:
                f.write(f"{head}\t{relation}\t{tail}\n")
        
        # Save as pickle for easy loading
        kg_data = {
            'entities': self.entities,
            'relations': self.relations,
            'triples': self.triples,
            'concepts': self.concepts,
            'num_entities': len(self.entities),
            'num_relations': len(self.relations),
            'num_triples': len(self.triples)
        }
        
        kg_pickle = os.path.join(self.output_path, 'mirflickr_25k_knowledge_graph.pkl')
        with open(kg_pickle, 'wb') as f:
            pickle.dump(kg_data, f)
        
        logger.info(f"Knowledge graph saved with {len(self.entities)} entities, "
                   f"{len(self.relations)} relations, and {len(self.triples)} triples")
    
    def build(self):
        """Build the complete knowledge graph."""
        logger.info("Starting MIR-Flickr-25K knowledge graph construction...")
        
        # Load data
        annotations = self.load_annotations()
        all_tags = self.load_tags()
        splits = self.load_split_indices()
        frequent_tags = self.extract_frequent_tags(all_tags)
        
        # Build relations
        self.build_image_concept_relations(annotations, splits)
        self.build_image_tag_relations(all_tags, frequent_tags)
        self.build_tag_concept_relations(all_tags, annotations, splits, frequent_tags)
        self.build_concept_cooccurrence_relations(annotations, splits)
        self.build_tag_cooccurrence_relations(all_tags, frequent_tags)
        
        # Save knowledge graph
        self.save_knowledge_graph()
        
        logger.info("MIR-Flickr-25K knowledge graph construction completed!")

def main():
    """Main function."""
    config = MIRFLICKR_DATASET_CONFIG
    data_path = config['data_path']
    kg_path = config['kg_path']
    
    builder = MIRFlickr25KKnowledgeGraphBuilder(data_path, kg_path)
    builder.build()

if __name__ == "__main__":
    main()
