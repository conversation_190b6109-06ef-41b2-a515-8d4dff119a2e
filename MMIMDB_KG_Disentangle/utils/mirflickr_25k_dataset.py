"""
Dataset loader for the complete MIR-Flickr-25K dataset.
This module handles loading of 25,000 images with their annotations and knowledge graph features.
"""

import os
import json
import numpy as np
import pickle
from PIL import Image
import torch
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import logging

logger = logging.getLogger(__name__)

class MIRFlickr25KDataset(Dataset):
    def __init__(self, data_path, kg_path, mode='train', transform=None):
        """
        Initialize MIR-Flickr-25K dataset.
        
        Args:
            data_path: Path to the MIR-Flickr data directory
            kg_path: Path to the knowledge graph data
            mode: 'train', 'val', or 'test'
            transform: Image transformations
        """
        self.data_path = data_path
        self.kg_path = kg_path
        self.mode = mode
        
        # Paths
        self.images_path = os.path.join(data_path, 'mirflickr')
        self.processed_path = os.path.join(data_path, 'processed')
        
        # Load data
        self._load_split_data()
        self._load_knowledge_graph()
        
        # Set up transforms
        if transform is None:
            if mode == 'train':
                self.transform = transforms.Compose([
                    transforms.Resize((224, 224)),
                    transforms.RandomHorizontalFlip(p=0.5),
                    transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                ])
            else:
                self.transform = transforms.Compose([
                    transforms.Resize((224, 224)),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                ])
        else:
            self.transform = transform
        
        logger.info(f"Loaded MIR-Flickr-25K {mode} dataset with {len(self.image_ids)} samples")
    
    def _load_split_data(self):
        """Load data for the specified split."""
        # Load image IDs
        split_file = os.path.join(self.processed_path, f'{self.mode}_ids_mirflickr_25k.txt')
        with open(split_file, 'r') as f:
            self.image_ids = [int(line.strip()) for line in f]
        
        # Load annotations
        ann_file = os.path.join(self.processed_path, 'annotations', f'{self.mode}_annotations.npy')
        self.annotations = np.load(ann_file)
        
        # Load tags
        tags_file = os.path.join(self.processed_path, 'tags', f'{self.mode}_tags.json')
        with open(tags_file, 'r') as f:
            self.tags_dict = json.load(f)
        
        # Load concepts
        concepts_file = os.path.join(self.processed_path, 'annotations', f'{self.mode}_concepts.txt')
        with open(concepts_file, 'r') as f:
            self.concepts = [line.strip() for line in f]
        
        assert len(self.image_ids) == len(self.annotations), "Mismatch between image IDs and annotations"
    
    def _load_knowledge_graph(self):
        """Load knowledge graph data."""
        kg_file = os.path.join(self.kg_path, 'mirflickr_25k_knowledge_graph.pkl')
        
        if os.path.exists(kg_file):
            with open(kg_file, 'rb') as f:
                self.kg_data = pickle.load(f)
            logger.info(f"Loaded knowledge graph with {self.kg_data['num_entities']} entities")
        else:
            logger.warning(f"Knowledge graph file not found: {kg_file}")
            self.kg_data = None
    
    def _get_kg_features(self, image_id, tags):
        """Extract knowledge graph features for an image."""
        if self.kg_data is None:
            return torch.zeros(200)  # Default KG feature dimension
        
        # Initialize feature vector
        kg_features = torch.zeros(200)
        
        try:
            entities = self.kg_data['entities']
            
            # Image entity features
            img_entity = f"image_{image_id}"
            if img_entity in entities:
                img_id = entities[img_entity]
                kg_features[img_id % 200] = 1.0
            
            # Tag entity features
            if tags:
                tag_list = tags.split()[:10]  # Limit to first 10 tags
                for tag in tag_list:
                    tag_entity = f"tag_{tag}"
                    if tag_entity in entities:
                        tag_id = entities[tag_entity]
                        kg_features[tag_id % 200] += 0.1
            
            # Concept entity features
            for i, concept in enumerate(self.concepts):
                if self.annotations[self.current_idx, i] == 1:
                    concept_entity = f"concept_{concept}"
                    if concept_entity in entities:
                        concept_id = entities[concept_entity]
                        kg_features[concept_id % 200] += 0.5
            
        except Exception as e:
            logger.warning(f"Error extracting KG features: {e}")
        
        return kg_features
    
    def _get_label_embeddings(self):
        """Get label embeddings for the current sample."""
        # Simple one-hot encoding for concepts
        label_embeddings = torch.zeros(len(self.concepts), 200)
        
        for i, concept in enumerate(self.concepts):
            if self.annotations[self.current_idx, i] == 1:
                label_embeddings[i, i % 200] = 1.0
        
        return label_embeddings
    
    def __len__(self):
        return len(self.image_ids)
    
    def __getitem__(self, idx):
        self.current_idx = idx  # Store for KG feature extraction
        
        # Get image ID and load image
        image_id = self.image_ids[idx]
        image_path = os.path.join(self.images_path, f'im{image_id}.jpg')
        
        try:
            image = Image.open(image_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
        except Exception as e:
            logger.warning(f"Error loading image {image_path}: {e}")
            # Create a dummy image
            image = torch.zeros(3, 224, 224)
        
        # Get annotations (labels)
        labels = torch.FloatTensor(self.annotations[idx])
        
        # Get tags
        tags = self.tags_dict.get(str(image_id), "")
        
        # Get KG features
        kg_features = self._get_kg_features(image_id, tags)
        
        # Get label embeddings
        label_embeddings = self._get_label_embeddings()
        
        # Create text features (simple bag of words)
        text_features = torch.zeros(300)  # Text feature dimension
        if tags:
            tag_list = tags.split()[:20]  # Limit to first 20 tags
            for i, tag in enumerate(tag_list):
                if i < 300:
                    text_features[i] = hash(tag) % 1000 / 1000.0  # Simple hash-based features
        
        return {
            'image': image,
            'text': text_features,
            'labels': labels,
            'kg_features': kg_features,
            'label_embeddings': label_embeddings,
            'image_id': image_id,
            'tags': tags
        }

def create_mirflickr_25k_dataloaders(data_path, kg_path, batch_size=32, num_workers=4):
    """Create data loaders for all splits."""
    
    datasets = {}
    dataloaders = {}
    
    for mode in ['train', 'val', 'test']:
        datasets[mode] = MIRFlickr25KDataset(
            data_path=data_path,
            kg_path=kg_path,
            mode=mode
        )
        
        shuffle = (mode == 'train')
        dataloaders[mode] = torch.utils.data.DataLoader(
            datasets[mode],
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=(mode == 'train')
        )
    
    return dataloaders, datasets

# For backward compatibility
def load_mirflickr_data(data_path, kg_path, mode='train'):
    """Load MIR-Flickr data (backward compatibility function)."""
    return MIRFlickr25KDataset(data_path, kg_path, mode)

if __name__ == "__main__":
    # Test the dataset
    from configs.mirflickr_config import MIRFLICKR_DATASET_CONFIG
    
    config = MIRFLICKR_DATASET_CONFIG
    dataset = MIRFlickr25KDataset(
        data_path=config['data_path'],
        kg_path=config['kg_path'],
        mode='train'
    )
    
    print(f"Dataset size: {len(dataset)}")
    sample = dataset[0]
    print(f"Sample keys: {sample.keys()}")
    print(f"Image shape: {sample['image'].shape}")
    print(f"Labels shape: {sample['labels'].shape}")
    print(f"KG features shape: {sample['kg_features'].shape}")
    print(f"Label embeddings shape: {sample['label_embeddings'].shape}")
    print(f"Text features shape: {sample['text'].shape}")
    print(f"Image ID: {sample['image_id']}")
    print(f"Tags: {sample['tags'][:100]}...")  # First 100 characters
