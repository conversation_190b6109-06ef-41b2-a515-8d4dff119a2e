"""
Training script for KG-Disentangle-Net on the complete MIR-Flickr-25K dataset.
This script trains the model on 25,000 images with comprehensive evaluation.
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
import json

from utils.mirflickr_25k_dataset import create_mirflickr_25k_dataloaders
from models.kg_disentangle_net import KGDisentangleNet
from utils.losses import compute_metrics
from configs.mirflickr_config import MIRFLICKR_DATASET_CONFIG, MIRFLICKR_MODEL_CONFIG, MIRFLICKR_TRAINING_CONFIG

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def train_epoch(model, dataloader, criterion, optimizer, device, args):
    """Train for one epoch."""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []

    # Gradient accumulation settings
    accumulation_steps = getattr(args, 'accumulation_steps', 4)
    effective_batch_size = args.batch_size * accumulation_steps

    progress_bar = tqdm(dataloader, desc="Training")

    for batch_idx, batch in enumerate(progress_bar):
        try:
            # Move data to device with non_blocking for efficiency
            images = batch['image'].to(device, non_blocking=True)
            text_features = batch['text'].to(device, non_blocking=True)
            labels = batch['labels'].to(device, non_blocking=True)
            kg_features = batch['kg_features'].to(device, non_blocking=True)
            label_embeddings = batch['label_embeddings'].to(device, non_blocking=True)

            # Forward pass with autocast for mixed precision
            with torch.cuda.amp.autocast():
                # Note: model returns (logits, disentanglement_loss)
                logits, disentanglement_loss = model(images, text_features, kg_features, label_embeddings)

                # Compute loss
                classification_loss = criterion(logits, labels)

                # Add disentanglement loss
                total_loss_batch = classification_loss + 0.1 * disentanglement_loss

                # Scale loss for gradient accumulation
                total_loss_batch = total_loss_batch / accumulation_steps

            # Backward pass with gradient scaling
            if hasattr(args, 'scaler'):
                args.scaler.scale(total_loss_batch).backward()
            else:
                total_loss_batch.backward()

            # Gradient accumulation
            if (batch_idx + 1) % accumulation_steps == 0:
                # Gradient clipping
                if hasattr(args, 'scaler'):
                    args.scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    args.scaler.step(optimizer)
                    args.scaler.update()
                else:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()

                optimizer.zero_grad()

            # Accumulate loss (scale back for logging)
            total_loss += total_loss_batch.item() * accumulation_steps

            # Store predictions and labels
            with torch.no_grad():
                preds = torch.sigmoid(logits).detach().cpu().numpy()
                labels_np = labels.detach().cpu().numpy()
                all_preds.append(preds)
                all_labels.append(labels_np)

            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{total_loss_batch.item() * accumulation_steps:.4f}',
                'Avg Loss': f'{total_loss/(batch_idx+1):.4f}',
                'GPU Mem': f'{torch.cuda.memory_allocated(device)/1024**3:.1f}GB'
            })

            # Clear cache periodically to prevent memory buildup
            if batch_idx % 50 == 0:
                torch.cuda.empty_cache()

        except RuntimeError as e:
            if "out of memory" in str(e):
                logger.error(f"CUDA OOM in batch {batch_idx}. Clearing cache and skipping batch.")
                torch.cuda.empty_cache()
                optimizer.zero_grad()  # Clear gradients to prevent state corruption
                continue
            else:
                logger.error(f"Runtime error in training batch {batch_idx}: {e}")
                continue
        except Exception as e:
            logger.error(f"Error in training batch {batch_idx}: {e}")
            continue

    # Compute metrics
    all_preds = np.concatenate(all_preds, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    metrics = compute_metrics(all_labels, all_preds)

    avg_loss = total_loss / len(dataloader)
    return avg_loss, metrics

def validate_epoch(model, dataloader, criterion, device):
    """Validate for one epoch."""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="Validating")):
            try:
                # Move data to device with non_blocking for efficiency
                images = batch['image'].to(device, non_blocking=True)
                text_features = batch['text'].to(device, non_blocking=True)
                labels = batch['labels'].to(device, non_blocking=True)
                kg_features = batch['kg_features'].to(device, non_blocking=True)
                label_embeddings = batch['label_embeddings'].to(device, non_blocking=True)

                # Forward pass with autocast for mixed precision
                with torch.cuda.amp.autocast():
                    logits, disentanglement_loss = model(images, text_features, kg_features, label_embeddings)

                    # Compute loss
                    loss = criterion(logits, labels)
                    total_loss += loss.item()

                # Store predictions and labels
                preds = torch.sigmoid(logits).detach().cpu().numpy()
                labels_np = labels.detach().cpu().numpy()
                all_preds.append(preds)
                all_labels.append(labels_np)

                # Clear cache periodically
                if batch_idx % 50 == 0:
                    torch.cuda.empty_cache()

            except RuntimeError as e:
                if "out of memory" in str(e):
                    logger.error(f"CUDA OOM in validation batch {batch_idx}. Clearing cache and skipping batch.")
                    torch.cuda.empty_cache()
                    continue
                else:
                    logger.error(f"Runtime error in validation batch {batch_idx}: {e}")
                    continue
            except Exception as e:
                logger.error(f"Error in validation batch {batch_idx}: {e}")
                continue

    # Compute metrics
    all_preds = np.concatenate(all_preds, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    metrics = compute_metrics(all_labels, all_preds)

    avg_loss = total_loss / len(dataloader)
    return avg_loss, metrics

def train_mirflickr_25k(args):
    """Main training function for MIR-Flickr-25K."""
    logger.info("Starting MIR-Flickr-25K training...")

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Log GPU memory info
    if torch.cuda.is_available():
        logger.info(f"GPU: {torch.cuda.get_device_name(device)}")
        logger.info(f"Total GPU memory: {torch.cuda.get_device_properties(device).total_memory / 1024**3:.1f} GB")
        logger.info(f"Available GPU memory: {torch.cuda.memory_reserved(device) / 1024**3:.1f} GB")

    # Set random seeds
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)

    # Add gradient accumulation and mixed precision support
    args.accumulation_steps = getattr(args, 'accumulation_steps', 4)
    args.scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None

    logger.info(f"Using gradient accumulation with {args.accumulation_steps} steps")
    logger.info(f"Effective batch size: {args.batch_size * args.accumulation_steps}")

    # Create data loaders
    logger.info("Creating data loaders...")
    dataloaders, datasets = create_mirflickr_25k_dataloaders(
        data_path=args.data_path,
        kg_path=args.kg_path,
        batch_size=args.batch_size,
        num_workers=2  # Reduce num_workers to save memory
    )

    train_loader = dataloaders['train']
    val_loader = dataloaders['val']
    test_loader = dataloaders['test']

    logger.info(f"Train dataset size: {len(datasets['train'])}")
    logger.info(f"Validation dataset size: {len(datasets['val'])}")
    logger.info(f"Test dataset size: {len(datasets['test'])}")

    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=300,
        visual_dim=4096,
        kg_dim=200,
        hidden_dim=512,
        num_classes=24  # Number of concepts in MIR-Flickr
    ).to(device)

    # Log model parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")

    # Create optimizer and scheduler
    optimizer = optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )

    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=3
    )

    # Loss function
    criterion = nn.BCEWithLogitsLoss()

    # Clear cache before training
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # Training loop
    best_f1 = 0
    best_model_path = None
    patience_counter = 0

    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_name = f"mirflickr_25k_kg_disentangle_{timestamp}"
    output_dir = os.path.join(args.output_dir, exp_name)
    os.makedirs(output_dir, exist_ok=True)

    # Save configuration
    config = {
        'args': vars(args),
        'model_config': MIRFLICKR_MODEL_CONFIG,
        'dataset_config': MIRFLICKR_DATASET_CONFIG,
        'training_config': MIRFLICKR_TRAINING_CONFIG
    }

    with open(os.path.join(output_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)

    logger.info(f"Starting training for {args.num_epochs} epochs...")

    for epoch in range(args.num_epochs):
        logger.info(f"Epoch {epoch+1}/{args.num_epochs}")

        # Train
        train_loss, train_metrics = train_epoch(model, train_loader, criterion, optimizer, device, args)

        # Validate
        val_loss, val_metrics = validate_epoch(model, val_loader, criterion, device)

        # Log metrics
        logger.info(f"Train Loss: {train_loss:.4f}, Train F1: {train_metrics['f1']:.4f}")
        logger.info(f"Val Loss: {val_loss:.4f}, Val F1: {val_metrics['f1']:.4f}")

        # Update learning rate
        scheduler.step(val_metrics['f1'])

        # Save best model
        if val_metrics['f1'] > best_f1:
            best_f1 = val_metrics['f1']
            best_model_path = os.path.join(output_dir, 'best_model.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_f1': best_f1,
                'val_metrics': val_metrics
            }, best_model_path)
            patience_counter = 0
            logger.info(f"New best model saved with F1: {best_f1:.4f}")
        else:
            patience_counter += 1

        # Early stopping
        if patience_counter >= args.patience:
            logger.info(f"Early stopping triggered after {epoch+1} epochs")
            break

    # Test evaluation
    logger.info("Evaluating on test set...")

    # Load best model
    if best_model_path:
        checkpoint = torch.load(best_model_path)
        model.load_state_dict(checkpoint['model_state_dict'])

    test_loss, test_metrics = validate_epoch(model, test_loader, criterion, device)

    logger.info("Test Results:")
    logger.info(f"Test Loss: {test_loss:.4f}")
    logger.info(f"Test F1: {test_metrics['f1']:.4f}")
    logger.info(f"Test Precision: {test_metrics['precision']:.4f}")
    logger.info(f"Test Recall: {test_metrics['recall']:.4f}")
    logger.info(f"Test mAP: {test_metrics['mAP']:.4f}")

    # Save test results
    with open(os.path.join(output_dir, 'test_results.json'), 'w') as f:
        json.dump(test_metrics, f, indent=2)

    logger.info(f"Training completed. Results saved to: {output_dir}")

    return best_model_path, test_metrics

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train KG-Disentangle-Net on MIR-Flickr-25K")

    # Data arguments
    parser.add_argument('--data_path', type=str,
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mirflickr',
                        help='Path to MIR-Flickr dataset')
    parser.add_argument('--kg_path', type=str,
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output_mirflickr_25k',
                        help='Output directory for results')

    # Training arguments
    parser.add_argument('--batch_size', type=int, default=4,
                        help='Batch size for training (reduced for memory efficiency)')
    parser.add_argument('--accumulation_steps', type=int, default=4,
                        help='Gradient accumulation steps (effective batch size = batch_size * accumulation_steps)')
    parser.add_argument('--num_epochs', type=int, default=30,
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='Weight decay')
    parser.add_argument('--patience', type=int, default=5,
                        help='Early stopping patience')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use for training')
    parser.add_argument('--mixed_precision', action='store_true', default=True,
                        help='Use mixed precision training')
    parser.add_argument('--memory_efficient', action='store_true', default=True,
                        help='Enable memory efficient training')

    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    train_mirflickr_25k(args)

if __name__ == "__main__":
    main()
