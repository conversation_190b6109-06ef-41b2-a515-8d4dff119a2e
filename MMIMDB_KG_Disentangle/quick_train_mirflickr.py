#!/usr/bin/env python3
"""
Quick training script for MIR-Flickr-25K with aggressive memory optimization.
This script is designed to work with limited GPU memory by using very small batch sizes.
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
from tqdm import tqdm
import gc

from utils.mirflickr_25k_dataset import create_mirflickr_25k_dataloaders
from models.kg_disentangle_net import KGDisentangleNet
from utils.losses import compute_metrics

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def clear_memory():
    """Aggressively clear GPU memory."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

def train_with_minimal_memory():
    """Train with minimal memory usage."""
    
    # Configuration
    BATCH_SIZE = 1  # Very small batch size
    ACCUMULATION_STEPS = 16  # Effective batch size = 16
    NUM_EPOCHS = 5
    LEARNING_RATE = 1e-4
    
    logger.info("Starting minimal memory training...")
    logger.info(f"Batch size: {BATCH_SIZE}, Accumulation: {ACCUMULATION_STEPS}")
    logger.info(f"Effective batch size: {BATCH_SIZE * ACCUMULATION_STEPS}")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    if torch.cuda.is_available():
        logger.info(f"GPU: {torch.cuda.get_device_name(device)}")
        logger.info(f"Total memory: {torch.cuda.get_device_properties(device).total_memory / 1024**3:.1f} GB")
        clear_memory()
    
    # Data paths
    data_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mirflickr'
    kg_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data'
    
    # Create data loaders
    logger.info("Creating data loaders...")
    try:
        dataloaders, datasets = create_mirflickr_25k_dataloaders(
            data_path=data_path,
            kg_path=kg_path,
            batch_size=BATCH_SIZE,
            num_workers=0  # No multiprocessing to save memory
        )
        
        train_loader = dataloaders['train']
        val_loader = dataloaders['val']
        
        logger.info(f"Train samples: {len(datasets['train'])}")
        logger.info(f"Val samples: {len(datasets['val'])}")
        
    except Exception as e:
        logger.error(f"Error creating data loaders: {e}")
        return
    
    # Create model
    logger.info("Creating model...")
    try:
        model = KGDisentangleNet(
            text_dim=300,
            visual_dim=4096,
            kg_dim=200,
            hidden_dim=512,
            num_classes=24
        ).to(device)
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        logger.info(f"Model parameters: {total_params:,}")
        
    except Exception as e:
        logger.error(f"Error creating model: {e}")
        return
    
    # Optimizer and loss
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-5)
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
    criterion = nn.BCEWithLogitsLoss()
    
    # Training loop
    best_f1 = 0
    
    for epoch in range(NUM_EPOCHS):
        logger.info(f"\nEpoch {epoch+1}/{NUM_EPOCHS}")
        clear_memory()
        
        # Training
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        optimizer.zero_grad()
        
        progress_bar = tqdm(train_loader, desc="Training")
        
        for batch_idx, batch in enumerate(progress_bar):
            try:
                # Move to device
                images = batch['image'].to(device, non_blocking=True)
                text = batch['text'].to(device, non_blocking=True)
                labels = batch['labels'].to(device, non_blocking=True)
                kg_features = batch['kg_features'].to(device, non_blocking=True)
                label_embeddings = batch['label_embeddings'].to(device, non_blocking=True)
                
                # Forward pass
                if scaler:
                    with torch.cuda.amp.autocast():
                        logits, disentangle_loss = model(images, text, kg_features, label_embeddings)
                        loss = (criterion(logits, labels) + 0.1 * disentangle_loss) / ACCUMULATION_STEPS
                    scaler.scale(loss).backward()
                else:
                    logits, disentangle_loss = model(images, text, kg_features, label_embeddings)
                    loss = (criterion(logits, labels) + 0.1 * disentangle_loss) / ACCUMULATION_STEPS
                    loss.backward()
                
                # Accumulate predictions
                with torch.no_grad():
                    preds = torch.sigmoid(logits).cpu().numpy()
                    labels_np = labels.cpu().numpy()
                    train_preds.append(preds)
                    train_labels.append(labels_np)
                
                train_loss += loss.item() * ACCUMULATION_STEPS
                
                # Gradient step
                if (batch_idx + 1) % ACCUMULATION_STEPS == 0:
                    if scaler:
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                        optimizer.step()
                    optimizer.zero_grad()
                
                # Clear tensors
                del images, text, labels, kg_features, label_embeddings, logits, disentangle_loss, loss
                
                # Update progress
                progress_bar.set_postfix({
                    'Loss': f'{train_loss/(batch_idx+1):.4f}',
                    'GPU': f'{torch.cuda.memory_allocated(device)/1024**3:.1f}GB' if torch.cuda.is_available() else 'N/A'
                })
                
                # Clear cache every 20 batches
                if batch_idx % 20 == 0:
                    clear_memory()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    logger.error(f"OOM at batch {batch_idx}, skipping...")
                    clear_memory()
                    optimizer.zero_grad()
                    continue
                else:
                    logger.error(f"Runtime error: {e}")
                    continue
            except Exception as e:
                logger.error(f"Error in batch {batch_idx}: {e}")
                continue
        
        # Final gradient step
        if len(train_loader) % ACCUMULATION_STEPS != 0:
            if scaler:
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                scaler.step(optimizer)
                scaler.update()
            else:
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
            optimizer.zero_grad()
        
        # Compute training metrics
        if train_preds and train_labels:
            train_preds = np.concatenate(train_preds, axis=0)
            train_labels = np.concatenate(train_labels, axis=0)
            train_metrics = compute_metrics(train_labels, train_preds)
            train_f1 = train_metrics['f1']
        else:
            train_f1 = 0.0
        
        avg_train_loss = train_loss / len(train_loader)
        logger.info(f"Train Loss: {avg_train_loss:.4f}, Train F1: {train_f1:.4f}")
        
        # Validation
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(val_loader, desc="Validation")):
                try:
                    images = batch['image'].to(device, non_blocking=True)
                    text = batch['text'].to(device, non_blocking=True)
                    labels = batch['labels'].to(device, non_blocking=True)
                    kg_features = batch['kg_features'].to(device, non_blocking=True)
                    label_embeddings = batch['label_embeddings'].to(device, non_blocking=True)
                    
                    if scaler:
                        with torch.cuda.amp.autocast():
                            logits, _ = model(images, text, kg_features, label_embeddings)
                            loss = criterion(logits, labels)
                    else:
                        logits, _ = model(images, text, kg_features, label_embeddings)
                        loss = criterion(logits, labels)
                    
                    val_loss += loss.item()
                    
                    preds = torch.sigmoid(logits).cpu().numpy()
                    labels_np = labels.cpu().numpy()
                    val_preds.append(preds)
                    val_labels.append(labels_np)
                    
                    del images, text, labels, kg_features, label_embeddings, logits, loss
                    
                    if batch_idx % 20 == 0:
                        clear_memory()
                        
                except Exception as e:
                    logger.error(f"Error in validation batch {batch_idx}: {e}")
                    continue
        
        # Compute validation metrics
        if val_preds and val_labels:
            val_preds = np.concatenate(val_preds, axis=0)
            val_labels = np.concatenate(val_labels, axis=0)
            val_metrics = compute_metrics(val_labels, val_preds)
            val_f1 = val_metrics['f1']
        else:
            val_f1 = 0.0
        
        avg_val_loss = val_loss / len(val_loader) if len(val_loader) > 0 else 0.0
        logger.info(f"Val Loss: {avg_val_loss:.4f}, Val F1: {val_f1:.4f}")
        
        # Save best model
        if val_f1 > best_f1:
            best_f1 = val_f1
            logger.info(f"New best F1: {best_f1:.4f}")
            
            # Save model
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_f1': best_f1,
            }, 'best_model_minimal_memory.pth')
    
    logger.info(f"Training completed! Best F1: {best_f1:.4f}")

if __name__ == "__main__":
    train_with_minimal_memory()
