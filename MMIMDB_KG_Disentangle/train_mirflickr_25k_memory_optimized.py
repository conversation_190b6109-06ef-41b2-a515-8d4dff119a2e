#!/usr/bin/env python3
"""
Memory-optimized training script for KG-Disentangle-Net on MIR-Flickr-25K dataset.
This script includes aggressive memory optimization techniques for training on limited GPU memory.
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
import json
import gc

from utils.mirflickr_25k_dataset import create_mirflickr_25k_dataloaders
from models.kg_disentangle_net import KGDisentangleNet
from utils.losses import compute_metrics
from configs.mirflickr_config import MIRFLICKR_DATASET_CONFIG, MIRFLICKR_MODEL_CONFIG, MIRFLICKR_TRAINING_CONFIG

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def clear_memory():
    """Clear GPU memory cache and run garbage collection."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

def train_epoch_memory_optimized(model, dataloader, criterion, optimizer, scaler, device, args):
    """Memory-optimized training for one epoch."""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    accumulation_steps = args.accumulation_steps
    progress_bar = tqdm(dataloader, desc="Training")
    
    # Initialize gradient accumulation
    optimizer.zero_grad()

    for batch_idx, batch in enumerate(progress_bar):
        try:
            # Move data to device
            images = batch['image'].to(device, non_blocking=True)
            text_features = batch['text'].to(device, non_blocking=True)
            labels = batch['labels'].to(device, non_blocking=True)
            kg_features = batch['kg_features'].to(device, non_blocking=True)
            label_embeddings = batch['label_embeddings'].to(device, non_blocking=True)

            # Forward pass with autocast
            with torch.cuda.amp.autocast():
                logits, disentanglement_loss = model(images, text_features, kg_features, label_embeddings)
                classification_loss = criterion(logits, labels)
                total_loss_batch = (classification_loss + 0.1 * disentanglement_loss) / accumulation_steps

            # Backward pass
            scaler.scale(total_loss_batch).backward()

            # Gradient accumulation
            if (batch_idx + 1) % accumulation_steps == 0:
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()

            # Accumulate loss
            total_loss += total_loss_batch.item() * accumulation_steps

            # Store predictions and labels (move to CPU immediately)
            with torch.no_grad():
                preds = torch.sigmoid(logits).detach().cpu().numpy()
                labels_np = labels.detach().cpu().numpy()
                all_preds.append(preds)
                all_labels.append(labels_np)

            # Clear intermediate tensors
            del images, text_features, labels, kg_features, label_embeddings
            del logits, disentanglement_loss, classification_loss, total_loss_batch

            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{total_loss/(batch_idx+1):.4f}',
                'GPU': f'{torch.cuda.memory_allocated(device)/1024**3:.1f}GB'
            })
            
            # Aggressive memory clearing
            if batch_idx % 10 == 0:
                clear_memory()

        except RuntimeError as e:
            if "out of memory" in str(e):
                logger.error(f"CUDA OOM in batch {batch_idx}. Clearing cache and skipping.")
                clear_memory()
                optimizer.zero_grad()
                continue
            else:
                logger.error(f"Runtime error in batch {batch_idx}: {e}")
                continue
        except Exception as e:
            logger.error(f"Error in batch {batch_idx}: {e}")
            continue

    # Final gradient step if needed
    if len(dataloader) % accumulation_steps != 0:
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        scaler.step(optimizer)
        scaler.update()
        optimizer.zero_grad()

    # Compute metrics
    if all_preds and all_labels:
        all_preds = np.concatenate(all_preds, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)
        metrics = compute_metrics(all_labels, all_preds)
    else:
        metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0, 'mAP': 0.0}

    avg_loss = total_loss / len(dataloader)
    return avg_loss, metrics

def validate_epoch_memory_optimized(model, dataloader, criterion, device):
    """Memory-optimized validation for one epoch."""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="Validating")):
            try:
                # Move data to device
                images = batch['image'].to(device, non_blocking=True)
                text_features = batch['text'].to(device, non_blocking=True)
                labels = batch['labels'].to(device, non_blocking=True)
                kg_features = batch['kg_features'].to(device, non_blocking=True)
                label_embeddings = batch['label_embeddings'].to(device, non_blocking=True)

                # Forward pass
                with torch.cuda.amp.autocast():
                    logits, _ = model(images, text_features, kg_features, label_embeddings)
                    loss = criterion(logits, labels)
                    total_loss += loss.item()

                # Store predictions and labels
                preds = torch.sigmoid(logits).detach().cpu().numpy()
                labels_np = labels.detach().cpu().numpy()
                all_preds.append(preds)
                all_labels.append(labels_np)
                
                # Clear intermediate tensors
                del images, text_features, labels, kg_features, label_embeddings, logits

                # Clear cache periodically
                if batch_idx % 20 == 0:
                    clear_memory()

            except RuntimeError as e:
                if "out of memory" in str(e):
                    logger.error(f"CUDA OOM in validation batch {batch_idx}. Skipping.")
                    clear_memory()
                    continue
                else:
                    logger.error(f"Runtime error in validation batch {batch_idx}: {e}")
                    continue
            except Exception as e:
                logger.error(f"Error in validation batch {batch_idx}: {e}")
                continue

    # Compute metrics
    if all_preds and all_labels:
        all_preds = np.concatenate(all_preds, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)
        metrics = compute_metrics(all_labels, all_preds)
    else:
        metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0, 'mAP': 0.0}

    avg_loss = total_loss / len(dataloader) if len(dataloader) > 0 else 0.0
    return avg_loss, metrics

def main():
    """Main function with memory optimization."""
    parser = argparse.ArgumentParser(description="Memory-optimized training for MIR-Flickr-25K")
    
    # Data arguments
    parser.add_argument('--data_path', type=str,
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mirflickr',
                        help='Path to MIR-Flickr dataset')
    parser.add_argument('--kg_path', type=str,
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output_mirflickr_25k_memory_opt',
                        help='Output directory for results')

    # Training arguments (memory optimized defaults)
    parser.add_argument('--batch_size', type=int, default=2,
                        help='Batch size for training (very small for memory efficiency)')
    parser.add_argument('--accumulation_steps', type=int, default=8,
                        help='Gradient accumulation steps (effective batch size = 16)')
    parser.add_argument('--num_epochs', type=int, default=10,
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='Weight decay')
    parser.add_argument('--patience', type=int, default=3,
                        help='Early stopping patience')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use for training')

    args = parser.parse_args()
    
    logger.info("Starting memory-optimized MIR-Flickr-25K training...")
    logger.info(f"Batch size: {args.batch_size}, Accumulation steps: {args.accumulation_steps}")
    logger.info(f"Effective batch size: {args.batch_size * args.accumulation_steps}")

    # Set device and clear memory
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    clear_memory()
    
    # Log GPU info
    if torch.cuda.is_available():
        logger.info(f"GPU: {torch.cuda.get_device_name(device)}")
        logger.info(f"Total GPU memory: {torch.cuda.get_device_properties(device).total_memory / 1024**3:.1f} GB")

    # Set seeds
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)

    # Create data loaders with minimal workers
    logger.info("Creating data loaders...")
    dataloaders, datasets = create_mirflickr_25k_dataloaders(
        data_path=args.data_path,
        kg_path=args.kg_path,
        batch_size=args.batch_size,
        num_workers=1  # Minimal workers to save memory
    )

    train_loader = dataloaders['train']
    val_loader = dataloaders['val']

    logger.info(f"Train dataset size: {len(datasets['train'])}")
    logger.info(f"Validation dataset size: {len(datasets['val'])}")

    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=300,
        visual_dim=4096,
        kg_dim=200,
        hidden_dim=512,
        num_classes=24
    ).to(device)

    # Optimizer and scaler
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scaler = torch.cuda.amp.GradScaler()
    criterion = nn.BCEWithLogitsLoss()

    # Training loop
    best_f1 = 0
    patience_counter = 0

    for epoch in range(args.num_epochs):
        logger.info(f"Epoch {epoch+1}/{args.num_epochs}")
        
        # Clear memory before each epoch
        clear_memory()

        # Train
        train_loss, train_metrics = train_epoch_memory_optimized(
            model, train_loader, criterion, optimizer, scaler, device, args
        )

        # Validate
        val_loss, val_metrics = validate_epoch_memory_optimized(
            model, val_loader, criterion, device
        )

        # Log metrics
        logger.info(f"Train Loss: {train_loss:.4f}, Train F1: {train_metrics['f1']:.4f}")
        logger.info(f"Val Loss: {val_loss:.4f}, Val F1: {val_metrics['f1']:.4f}")

        # Early stopping
        if val_metrics['f1'] > best_f1:
            best_f1 = val_metrics['f1']
            patience_counter = 0
            logger.info(f"New best F1: {best_f1:.4f}")
        else:
            patience_counter += 1

        if patience_counter >= args.patience:
            logger.info(f"Early stopping after {epoch+1} epochs")
            break

    logger.info("Training completed!")

if __name__ == "__main__":
    main()
