#!/usr/bin/env python3
"""
Test script to determine optimal batch size for MIR-Flickr-25K training.
This script tests different batch sizes to find the maximum that fits in GPU memory.
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from utils.mirflickr_25k_dataset import create_mirflickr_25k_dataloaders
from models.kg_disentangle_net import KGDisentangleNet

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_batch_size(batch_size, device, data_path, kg_path):
    """Test if a specific batch size fits in memory."""
    try:
        logger.info(f"Testing batch size: {batch_size}")
        
        # Clear cache
        torch.cuda.empty_cache()
        
        # Create data loader
        dataloaders, _ = create_mirflickr_25k_dataloaders(
            data_path=data_path,
            kg_path=kg_path,
            batch_size=batch_size,
            num_workers=1
        )
        
        # Create model
        model = KGDisentangleNet(
            text_dim=300,
            visual_dim=4096,
            kg_dim=200,
            hidden_dim=512,
            num_classes=24
        ).to(device)
        
        # Test forward pass
        train_loader = dataloaders['train']
        batch = next(iter(train_loader))
        
        images = batch['image'].to(device)
        text_features = batch['text'].to(device)
        labels = batch['labels'].to(device)
        kg_features = batch['kg_features'].to(device)
        label_embeddings = batch['label_embeddings'].to(device)
        
        # Forward pass
        with torch.cuda.amp.autocast():
            logits, disentanglement_loss = model(images, text_features, kg_features, label_embeddings)
        
        # Test backward pass
        criterion = nn.BCEWithLogitsLoss()
        loss = criterion(logits, labels) + 0.1 * disentanglement_loss
        loss.backward()
        
        # Log memory usage
        memory_allocated = torch.cuda.memory_allocated(device) / 1024**3
        memory_reserved = torch.cuda.memory_reserved(device) / 1024**3
        
        logger.info(f"Batch size {batch_size}: SUCCESS")
        logger.info(f"  Memory allocated: {memory_allocated:.2f} GB")
        logger.info(f"  Memory reserved: {memory_reserved:.2f} GB")
        
        # Clean up
        del model, batch, images, text_features, labels, kg_features, label_embeddings
        del logits, disentanglement_loss, loss
        torch.cuda.empty_cache()
        
        return True, memory_allocated, memory_reserved
        
    except RuntimeError as e:
        if "out of memory" in str(e):
            logger.info(f"Batch size {batch_size}: OUT OF MEMORY")
            torch.cuda.empty_cache()
            return False, 0, 0
        else:
            logger.error(f"Batch size {batch_size}: ERROR - {e}")
            torch.cuda.empty_cache()
            return False, 0, 0
    except Exception as e:
        logger.error(f"Batch size {batch_size}: UNEXPECTED ERROR - {e}")
        torch.cuda.empty_cache()
        return False, 0, 0

def find_optimal_batch_size(device, data_path, kg_path):
    """Find the optimal batch size through binary search."""
    logger.info("Finding optimal batch size...")
    
    # Test batch sizes from 1 to 32
    batch_sizes = [1, 2, 4, 6, 8, 12, 16, 20, 24, 28, 32]
    
    max_working_batch_size = 1
    memory_usage = {}
    
    for batch_size in batch_sizes:
        success, mem_alloc, mem_reserved = test_batch_size(batch_size, device, data_path, kg_path)
        
        if success:
            max_working_batch_size = batch_size
            memory_usage[batch_size] = (mem_alloc, mem_reserved)
        else:
            break
    
    logger.info(f"\nOptimal batch size: {max_working_batch_size}")
    
    if max_working_batch_size in memory_usage:
        mem_alloc, mem_reserved = memory_usage[max_working_batch_size]
        logger.info(f"Memory usage at optimal batch size:")
        logger.info(f"  Allocated: {mem_alloc:.2f} GB")
        logger.info(f"  Reserved: {mem_reserved:.2f} GB")
    
    # Suggest gradient accumulation
    target_effective_batch_size = 16
    accumulation_steps = max(1, target_effective_batch_size // max_working_batch_size)
    effective_batch_size = max_working_batch_size * accumulation_steps
    
    logger.info(f"\nRecommended settings:")
    logger.info(f"  --batch_size {max_working_batch_size}")
    logger.info(f"  --accumulation_steps {accumulation_steps}")
    logger.info(f"  Effective batch size: {effective_batch_size}")
    
    return max_working_batch_size, accumulation_steps

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test memory usage for MIR-Flickr-25K training")
    parser.add_argument('--data_path', type=str,
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mirflickr',
                        help='Path to MIR-Flickr dataset')
    parser.add_argument('--kg_path', type=str,
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use for testing')
    
    args = parser.parse_args()
    
    # Check device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    
    if not torch.cuda.is_available():
        logger.error("CUDA not available. This script is designed for GPU testing.")
        return
    
    logger.info(f"Testing on device: {device}")
    logger.info(f"GPU: {torch.cuda.get_device_name(device)}")
    logger.info(f"Total GPU memory: {torch.cuda.get_device_properties(device).total_memory / 1024**3:.1f} GB")
    
    # Clear any existing memory
    torch.cuda.empty_cache()
    
    # Find optimal batch size
    optimal_batch_size, accumulation_steps = find_optimal_batch_size(device, args.data_path, args.kg_path)
    
    logger.info(f"\nTo run training with optimal settings:")
    logger.info(f"python train_mirflickr_25k_memory_optimized.py \\")
    logger.info(f"  --batch_size {optimal_batch_size} \\")
    logger.info(f"  --accumulation_steps {accumulation_steps} \\")
    logger.info(f"  --num_epochs 10")

if __name__ == "__main__":
    main()
