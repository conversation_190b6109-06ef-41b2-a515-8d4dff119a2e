"""
Simple test script for the MIR-Flickr-25K dataset loader.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.mirflickr_25k_dataset import MIRFlickr25KDataset
from configs.mirflickr_config import MIRFLICKR_DATASET_CONFIG

def test_dataset():
    """Test the dataset loading."""
    print("Testing MIR-Flickr-25K dataset...")
    
    config = MIRFLICKR_DATASET_CONFIG
    
    try:
        # Test train dataset
        print("Loading train dataset...")
        train_dataset = MIRFlickr25KDataset(
            data_path=config['data_path'],
            kg_path=config['kg_path'],
            mode='train'
        )
        print(f"Train dataset size: {len(train_dataset)}")
        
        # Test a sample
        print("Loading first sample...")
        sample = train_dataset[0]
        print(f"Sample keys: {sample.keys()}")
        print(f"Image shape: {sample['image'].shape}")
        print(f"Labels shape: {sample['labels'].shape}")
        print(f"KG features shape: {sample['kg_features'].shape}")
        print(f"Text features shape: {sample['text'].shape}")
        print(f"Image ID: {sample['image_id']}")
        print(f"Number of positive labels: {sample['labels'].sum().item()}")
        
        print("Dataset test completed successfully!")
        
    except Exception as e:
        print(f"Error testing dataset: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dataset()
